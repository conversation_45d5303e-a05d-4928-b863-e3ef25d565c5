import React from 'react';
import { Chatbot } from '@/components/Chatbot/Chatbot';

export default function TestChatPage() {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">
          Chat Test Page
        </h1>
        <div className="bg-white rounded-lg shadow-lg p-6">
          <p className="text-gray-600 mb-4">
            This page is for testing the chatbot functionality. The chatbot should appear in the bottom right corner.
          </p>
          <p className="text-gray-600">
            Try sending a message to test the SSE streaming functionality with the backend API.
          </p>
        </div>
      </div>
      
      {/* Chatbot component */}
      <Chatbot 
        position="sidebar" 
        defaultOpen={true}
        initialMessage="Hello! I'm OpenStory AI. I'm here to help you create amazing stories. What would you like to create today?"
      />
    </div>
  );
}
