import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import styled from 'styled-components';
import { ChevronDown, ChevronUp, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { koho } from "@/theme"; // Import KoHo font config

// Styled send button with SVG background
const SendButton = styled.button`
  background: #2389e9;
  padding: 0;
  width: 50px;
  height: 50px;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  border-radius: 16px;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.5s ease-in-out;
  z-index: 2;

  &:hover {
    border-radius: 50%;
    transition: all 0.5s ease-in-out;
  }
  &:hover:before {
    margin-left: 0%;
    transform: rotate(24deg);
  }
  &::before {
    content: "";
    background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNzUycHQiIGhlaWdodD0iNzUycHQiIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDc1MiA3NTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8cGF0aCBkPSJtNTczLjE4IDE5OC42MnYwbC0zOTYuMDkgNjMuNzE5Yy03Ljc1IDAuODU5MzgtOS40NzI3IDExLjE5NS0zLjQ0NTMgMTUuNWw5Ny4zMDEgNjguODgzLTE1LjUgMTEyLjhjLTAuODU5MzggNy43NSA3Ljc1IDEyLjkxNCAxMy43NzcgNy43NWw1NS4xMDktNDQuNzczIDI2LjY5MSAxMjQuODVjMS43MjI3IDcuNzUgMTEuMTk1IDkuNDcyNyAxNS41IDIuNTgybDIxNS4yNy0zMzguMzljMy40NDE0LTYuMDI3My0xLjcyNjYtMTMuNzc3LTguNjEzMy0xMi45MTR6bS0zNzIuODQgNzYuNjMzIDMxMy40Mi00OS45NDEtMjMzLjM0IDEwNy42M3ptNzQuMDUxIDE2NS4zMiAxMi45MTQtOTIuMTMzYzgwLjkzOC0zNy4wMjcgMTM5LjQ5LTY0LjU3OCAyMjkuMDQtMTA1LjkxLTEuNzE4OCAxLjcyMjctMC44NTkzNyAwLjg1OTM4LTI0MS45NSAxOTguMDR6bTg4LjY4OCA4Mi42Ni0yNC4xMDktMTEyLjggMTk5Ljc3LTE2Mi43NHoiIGZpbGw9IiNmZmYiLz4KPC9zdmc+Cg==");
    height: 50px;
    background-repeat: no-repeat;
    position: absolute;
    width: 50px;
    transition: all 0.9s ease-in-out;
    background-size: 100%;
    left: 0;
    top: 0;
  }
`;

// 添加聊天容器的样式组件
const ChatContainer = styled.div`
  border: 2px solid #d1d5db;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
  max-height: 65vh;
  display: flex;
  flex-direction: column;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.05) 0%,
      rgba(147, 51, 234, 0.05) 100%
    );
    pointer-events: none;
    z-index: 0;
  }

  & > * {
    position: relative;
    z-index: 1;
  }
`;

interface Message {
  id: string;
  content: string;
  sender: "user" | "bot";
  timestamp: Date;
}

interface ChatbotProps {
  initialMessage?: string;
  position?: "floating" | "sidebar";
  defaultOpen?: boolean; // 新增默认展开状态属性
}

const Chatbot: React.FC<ChatbotProps> = ({
  initialMessage = "I've added some stories and characters! Take a look",
  position = "floating",
  defaultOpen = true,
}) => {
  const [isOpen, setIsOpen] = useState(
    position === "sidebar" ? defaultOpen : false
  );
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to the bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Add initial message when component mounts
  useEffect(() => {
    if (initialMessage) {
      setMessages([
        {
          id: "1",
          content: initialMessage,
          sender: "bot",
          timestamp: new Date(),
        },
      ]);
    }
  }, [initialMessage]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: "user",
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setError(null);

    // Set loading state
    setIsLoading(true);

    try {
      // In a real app, this would be an API call to your LLM backend
      // const response = await sendMessage(inputValue);

      // Simulate API call with timeout
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content:
          "I'm here to help you create amazing stories. What would you like to create today?",
        sender: "bot",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, botMessage]);
    } catch (err) {
      console.error("Error sending message:", err);
      setError("Failed to get a response. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleChat = () => {
    setIsOpen((prev) => !prev);
    if (!isOpen) {
      // Focus the input when opening
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  // Format time as HH:MM
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <>
      {/* Floating button when chat is closed */}
      {!isOpen && position === "floating" && (
        <button
          onClick={toggleChat}
          className="fixed bottom-5 right-5 bg-blue-500 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg hover:bg-blue-600 transition-colors z-50"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
            />
          </svg>
        </button>
      )}

      {/* Chat container - KoHo font applied */}
      {(isOpen || position === "sidebar") && (
        <div
          className={cn(
            "fixed bottom-32 right-5 z-40 flex flex-col gap-4 w-[380px]",
            koho.className
          )}
        >
          {/* 收起/展开按钮 - 在 sidebar 模式下始终显示 */}
          {position === "sidebar" && (
            <div className="self-end mb-2">
              <button
                onClick={toggleChat}
                className="bg-white/95 backdrop-blur-sm rounded-full p-3 shadow-lg hover:shadow-xl hover:bg-white transition-all duration-300 border-2 border-gray-200 hover:border-blue-300 group"
                aria-label={isOpen ? "Collapse chat" : "Expand chat"}
              >
                <div className="text-gray-600 group-hover:text-blue-600 transition-colors duration-300">
                  {isOpen ? <ChevronDown size={20} /> : <ChevronUp size={20} />}
                </div>
              </button>
            </div>
          )}

          {/* 使用新的聊天容器组件 */}
          {isOpen && (
            <ChatContainer>
              {/* 消息列表区域 */}
              <div
                className="flex-1 overflow-y-auto p-4 flex flex-col gap-4"
                ref={messagesContainerRef}
              >
                {/* Messages in chronological order (oldest first) */}
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "max-w-[95%] animate-slideIn",
                      message.sender === "user" ? "self-end" : "self-start"
                    )}
                  >
                    {message.sender === "user" ? (
                      // User message bubble with enhanced styling
                      <div
                        className="bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-2xl rounded-br-none shadow-lg hover:shadow-xl p-4 font-koho text-[16px] border-2 border-blue-400 transition-all duration-300"
                        style={{
                          boxShadow:
                            "0 8px 20px rgba(59, 130, 246, 0.3), 0 4px 8px rgba(59, 130, 246, 0.2)",
                        }}
                      >
                        <p className="text-[14px] leading-[21px] font-koho">
                          {message.content}
                        </p>
                        <span className="text-xs mt-1 block text-blue-100 text-right font-koho">
                          {formatTime(message.timestamp)}
                        </span>
                      </div>
                    ) : (
                      // Bot message card - enhanced visual contrast
                      <div
                        className="w-full bg-white/95 backdrop-blur-sm shadow-lg hover:shadow-xl rounded-2xl p-6 flex items-start gap-3 border-2 border-gray-300 font-koho transition-all duration-300"
                        style={{
                          boxShadow:
                            "0 8px 20px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)",
                        }}
                      >
                        <div className="w-6 h-6 rounded-full bg-[#F6FAFF] flex-shrink-0 flex items-center justify-center overflow-hidden shadow-[0px_-2px_4px_rgba(35,136,255,0.08)_inset]">
                          <Image
                            src="/images/logo/logo-metal.png"
                            alt="OpenStory AI"
                            width={24}
                            height={24}
                            className="rounded-full object-cover w-[24.75px] h-[24.75px]"
                          />
                        </div>
                        <div className="flex-1 flex flex-col gap-2">
                          <div className="flex items-center gap-3">
                            <span className="text-[14px] font-medium text-[#19213D] leading-[18.2px] font-koho">
                              OpenStory AI
                            </span>
                            <div className="h-4 w-px bg-[#F0F2F5] rotate-90"></div>
                            <span className="text-[12px] font-medium text-[#666F8D] leading-[15.6px] font-koho">
                              {formatTime(message.timestamp)}
                            </span>
                          </div>
                          <p className="text-[14px] text-[#666F8D] leading-[21px] font-koho">
                            {message.content}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {/* Loading indicator */}
                {isLoading && (
                  <div
                    className="flex items-center space-x-2 p-4 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg self-start ml-1 mt-2 font-koho border-2 border-gray-200 transition-all duration-300"
                    style={{
                      boxShadow:
                        "0 8px 20px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)",
                    }}
                  >
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce delay-150" />
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce delay-300" />
                  </div>
                )}

                {/* Error message */}
                {error && (
                  <div
                    className="flex items-center gap-2 p-4 bg-red-50/95 backdrop-blur-sm text-red-600 rounded-xl w-full mb-1 border-2 border-red-200 shadow-lg font-koho transition-all duration-300"
                    style={{
                      boxShadow:
                        "0 8px 20px rgba(239, 68, 68, 0.15), 0 4px 8px rgba(239, 68, 68, 0.1)",
                    }}
                  >
                    <AlertCircle size={16} />
                    <p className="text-sm">{error}</p>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </ChatContainer>
          )}
        </div>
      )}

      {/* 输入区域 - 仅在聊天框展开时显示 */}
      {isOpen && (
        <div
          className={cn(
            "fixed bottom-5 right-5 w-[380px] z-50 font-koho",
            koho.className
          )}
        >
          <form
            onSubmit={handleSubmit}
            className="bg-white/98 backdrop-blur-sm rounded-2xl shadow-xl border-2 border-gray-200 px-8 pt-8 pb-8 flex flex-col gap-0 relative hover:shadow-2xl transition-all duration-300"
            style={{
              boxShadow:
                "0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8)",
            }}
          >
            <div className="relative w-full">
              <input
                id="chatbot-input"
                ref={inputRef}
                type="text"
                placeholder="Help me with...."
                value={inputValue}
                onChange={handleInputChange}
                disabled={isLoading}
                className="w-full bg-transparent border-none outline-none text-sm text-[#666F8D] font-koho placeholder-[#B0B7C3] pr-16"
                style={{ fontFamily: "var(--font-koho), KoHo, sans-serif" }}
              />
              <SendButton
                type="submit"
                disabled={!inputValue.trim() || isLoading}
                tabIndex={-1}
                aria-label="Send"
                style={{
                  opacity: inputValue.trim() && !isLoading ? 1 : 0.5,
                  pointerEvents:
                    inputValue.trim() && !isLoading ? "auto" : "none",
                }}
              />
              {/* 分隔线 */}
              <div className="absolute left-0 right-0 bottom-0 h-px bg-[#F0F2F5]" />
            </div>
          </form>
        </div>
      )}
    </>
  );
};

export default Chatbot; 
